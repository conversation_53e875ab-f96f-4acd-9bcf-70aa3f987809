import React from 'react';
import { Helmet } from 'react-helmet-async';
import Dingbudaohanglan from '../../zujian/dingbudaohanglan/dingbudaohanglan.jsx';
import { tiqu_dingbu_daohang_xinxi, cong_huancun_duqu_wanzheng_shuju } from '../../gongju/wangzhanjichuxinxi_huancun.js';

const Guaiwushuju = () => {
  const [daohang_shuju, shezhi_daohang_shuju] = React.useState({
    wangzhan_mingcheng: 'RO百科',
    wangzhan_tubiao_lianjie: 'https://pan.new-cdn.com/f/GONUG/yunluo.jpg',
    dingbu_daohang: []
  });

  // 加载导航数据
  React.useEffect(() => {
    const wanzheng_shuju = cong_huancun_duqu_wanzheng_shuju();
    if (wanzheng_shuju) {
      const daohang_xinxi = tiqu_dingbu_daohang_xinxi(wanzheng_shuju);
      if (daohang_xinxi) {
        shezhi_daohang_shuju({
          wangzhan_mingcheng: daohang_xinxi.wangzhan_mingcheng || 'RO百科',
          wangzhan_tubiao_lianjie: daohang_xinxi.wangzhan_tubiao_lianjie || 'https://pan.new-cdn.com/f/GONUG/yunluo.jpg',
          dingbu_daohang: daohang_xinxi.dingbu_daohang || []
        });
      }
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>怪物数据 - RO百科</title>
        <meta name="description" content="仙境传说怪物数据查询" />
      </Helmet>
      
      <Dingbudaohanglan
        wangzhanmingcheng={daohang_shuju.wangzhan_mingcheng}
        wangzhanlogo={daohang_shuju.wangzhan_tubiao_lianjie}
        caidanxiangmu={daohang_shuju.dingbu_daohang}
        xianshi={true}
      />
      
      <div style={{ padding: '20px', marginTop: '80px' }}>
        <h1>怪物数据</h1>
        <p>这里将显示仙境传说的怪物数据信息。</p>
      </div>
    </>
  );
};

export default Guaiwushuju;
