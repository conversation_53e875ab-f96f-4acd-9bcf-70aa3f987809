<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <title>RO百科</title>
    <style>
      html, body {
        margin: 0;
        padding: 0;
        min-height: 100%;
        height: 100%;
        /* 确保背景色能够正确继承 */
        background: transparent;
        /* 隐藏滚动条但保持滚动功能 */
        overflow-x: hidden;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
      }

      /* Webkit浏览器（Chrome, Safari等）隐藏滚动条 */
      html::-webkit-scrollbar,
      body::-webkit-scrollbar {
        display: none;
      }

      /* 禁用移动端双击缩放 */
      * {
        touch-action: manipulation;
      }

      #root {
        min-height: 100vh;
        height: 100%;
        /* 确保根元素背景色能够正确显示 */
        background: transparent;
      }

      /* 初始预加载样式 - 根据用户主题偏好显示 */
      .chushi-yujiazai {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100vw;
        height: 100vh;
        /* 默认明亮主题背景 */
        background: linear-gradient(135deg,
          #f5f3ff 0%,
          #ede9fe 30%,
          #ddd6fe 60%,
          #f0f4f8 100%
        );
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(15px);
      }

      /* 暗色主题偏好时使用暗色背景 */
      @media (prefers-color-scheme: dark) {
        .chushi-yujiazai {
          background: linear-gradient(135deg,
            #0a0a0f 0%,
            #1a1a2e 30%,
            #16213e 60%,
            #0f0f23 100%
          );
        }
      }

      /* 如果localStorage中有明确的主题设置，优先使用 */
      .chushi-yujiazai.zhuti-mingliang {
        background: linear-gradient(135deg,
          #f5f3ff 0%,
          #ede9fe 30%,
          #ddd6fe 60%,
          #f0f4f8 100%
        ) !important;
      }

      .chushi-yujiazai.zhuti-anhei {
        background: linear-gradient(135deg,
          #0a0a0f 0%,
          #1a1a2e 30%,
          #16213e 60%,
          #0f0f23 100%
        ) !important;
      }

      .chushi-yujiazai::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        /* 默认明亮主题光效 */
        background: radial-gradient(ellipse at 30% 20%,
          rgba(139, 92, 246, 0.15) 0%,
          transparent 50%
        ),
        radial-gradient(ellipse at 70% 80%,
          rgba(167, 139, 250, 0.12) 0%,
          transparent 50%
        );
        pointer-events: none;
        animation: qiliang_bianhua 8s ease-in-out infinite;
      }

      /* 暗色主题偏好时使用暗色光效 */
      @media (prefers-color-scheme: dark) {
        .chushi-yujiazai::before {
          background: radial-gradient(ellipse at 30% 20%,
            rgba(70, 130, 180, 0.1) 0%,
            transparent 50%
          ),
          radial-gradient(ellipse at 70% 80%,
            rgba(100, 149, 237, 0.08) 0%,
            transparent 50%
          );
        }
      }

      @keyframes qiliang_bianhua {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 0.4; }
      }

      /* 旋转器主题适配 */
      .chushi-xuanzhuanqi {
        /* 默认明亮主题颜色 */
        border-top: 4px solid rgba(139, 92, 246, 0.8) !important;
        border-right: 4px solid rgba(139, 92, 246, 0.4) !important;
        border-bottom: 4px solid rgba(139, 92, 246, 0.2) !important;
        border-left: 4px solid rgba(139, 92, 246, 0.1) !important;
        box-shadow: 0 0 40px rgba(139, 92, 246, 0.3) !important;
      }

      /* 暗色主题偏好时使用暗色旋转器 */
      @media (prefers-color-scheme: dark) {
        .chushi-xuanzhuanqi {
          border-top: 4px solid rgba(135, 206, 235, 0.8) !important;
          border-right: 4px solid rgba(135, 206, 235, 0.4) !important;
          border-bottom: 4px solid rgba(135, 206, 235, 0.2) !important;
          border-left: 4px solid rgba(135, 206, 235, 0.1) !important;
          box-shadow: 0 0 40px rgba(135, 206, 235, 0.3) !important;
        }
      }

      /* React组件加载后隐藏初始预加载 */
      .react-loaded .chushi-yujiazai {
        display: none;
      }
    </style>
  </head>
  <body>
    <!-- 初始预加载动画 -->
    <div class="chushi-yujiazai" id="chushi-yujiazai">
      <div class="chushi-xuanzhuanqi" style="
        width: 140px;
        height: 140px;
        border: 4px solid transparent;
        border-top: 4px solid rgba(139, 92, 246, 0.8);
        border-right: 4px solid rgba(139, 92, 246, 0.4);
        border-bottom: 4px solid rgba(139, 92, 246, 0.2);
        border-left: 4px solid rgba(139, 92, 246, 0.1);
        border-radius: 50%;
        animation: xuanzhuan 4s linear infinite;
        box-shadow: 0 0 40px rgba(139, 92, 246, 0.3);
      "></div>
    </div>

    <div id="root"></div>

    <style>
      @keyframes xuanzhuan {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>

    <script>
      // 根据localStorage中的主题设置调整初始预加载动画
      (function() {
        try {
          const chushiYujiazai = document.getElementById('chushi-yujiazai');
          if (!chushiYujiazai) return;

          // 尝试从localStorage读取主题设置
          let cunchu_zhuti = localStorage.getItem('minganbuju_zhuti');

          // 处理可能的JSON字符串格式
          if (cunchu_zhuti) {
            try {
              // 如果是JSON字符串，解析它
              if (cunchu_zhuti.startsWith('"') && cunchu_zhuti.endsWith('"')) {
                cunchu_zhuti = JSON.parse(cunchu_zhuti);
              }
            } catch (e) {
  
            }
          }



          // 根据存储的主题或系统偏好设置样式
          if (cunchu_zhuti === 'mingliang') {
            chushiYujiazai.classList.add('zhuti-mingliang');
          } else if (cunchu_zhuti === 'anhei') {
            chushiYujiazai.classList.add('zhuti-anhei');
          } else {
            // 没有存储的主题设置，使用系统偏好
            const prefers_dark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefers_dark) {
              chushiYujiazai.classList.add('zhuti-anhei');
            } else {
              chushiYujiazai.classList.add('zhuti-mingliang');
            }
          }
        } catch (error) {
          // 出错时默认使用明亮主题
          const chushiYujiazai = document.getElementById('chushi-yujiazai');
          if (chushiYujiazai) {
            chushiYujiazai.classList.add('zhuti-mingliang');
          }
        }
      })();
    </script>
  </body>
</html>
